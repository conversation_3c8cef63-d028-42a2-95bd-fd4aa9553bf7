* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 3rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

/* 上传区域样式 */
.upload-section {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.upload-area {
    text-align: center;
    border: 3px dashed #ddd;
    border-radius: 10px;
    padding: 40px;
    transition: all 0.3s ease;
}

.upload-area:hover {
    border-color: #667eea;
    background-color: #f8f9ff;
}

.upload-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.upload-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.file-info {
    margin-top: 20px;
    padding: 20px;
    background: #f8f9ff;
    border-radius: 10px;
    text-align: center;
}

/* 抽奖设置区域 */
.lottery-settings {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.settings-panel h3 {
    text-align: center;
    color: #667eea;
    margin-bottom: 30px;
    font-size: 1.8rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.setting-item label {
    font-weight: bold;
    color: #555;
    font-size: 1.1rem;
}

.setting-item select,
.setting-item input {
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.setting-item select:focus,
.setting-item input:focus {
    outline: none;
    border-color: #667eea;
}

.rounds-config {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.rounds-config h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.round-setting {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.round-setting label {
    min-width: 100px;
    font-weight: bold;
    color: #555;
}

.round-setting input {
    flex: 1;
    max-width: 150px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.settings-actions {
    text-align: center;
    margin-top: 30px;
}

/* 抽奖控制区域 */
.lottery-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.control-panel {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.lottery-info {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 10px;
    flex: 1;
    min-width: 200px;
}

.lottery-info p {
    margin: 5px 0;
    font-size: 1.1rem;
}

.lottery-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
    flex: 1;
    min-width: 300px;
}

.current-lottery-info {
    background: #e7f3ff;
    border: 2px solid #667eea;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.current-lottery-info h4 {
    color: #667eea;
    font-size: 1.4rem;
    margin-bottom: 10px;
}

.current-lottery-info p {
    color: #555;
    font-size: 1.1rem;
    margin: 0;
}

/* 按钮样式 */
.btn {
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-weight: bold;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-large {
    padding: 20px 40px;
    font-size: 1.3rem;
}

.btn-medium {
    padding: 15px 30px;
    font-size: 1.2rem;
}

.btn-small {
    padding: 10px 20px;
    font-size: 0.9rem;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-secondary {
    background: #28a745;
    color: white;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

/* 结果展示区域 */
.results-section {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.lottery-animation {
    text-align: center;
    color: white;
}

.lottery-animation h2 {
    font-size: 3rem;
    margin-bottom: 30px;
    animation: pulse 1s infinite;
}

.rolling-names {
    font-size: 2rem;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffd700;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.results-display {
    background: white;
    border-radius: 20px;
    padding: 40px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    text-align: center;
}

.results-display .btn {
    padding: 20px 60px;
    font-size: 1.5rem;
    margin-top: 30px;
    min-width: 200px;
}

.results-display h2 {
    font-size: 2.5rem;
    margin-bottom: 30px;
    color: #667eea;
}

.winners-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 15px;
    margin-bottom: 30px;
    max-height: 60vh;
    overflow-y: auto;
}

.winner-card {
    background: #f8f9ff;
    border: 2px solid #667eea;
    border-radius: 10px;
    padding: 15px;
    text-align: left;
}

.winner-card h4 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.winner-card p {
    margin: 5px 0;
    font-size: 0.9rem;
    color: #666;
}

/* 历史记录 */
.history-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.history-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-btn {
    padding: 10px 20px;
    border: 2px solid #667eea;
    background: white;
    color: #667eea;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #667eea;
    color: white;
}

.history-content {
    min-height: 200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .control-panel {
        flex-direction: column;
    }
    
    .winners-grid {
        grid-template-columns: 1fr;
    }
    
    .results-display {
        padding: 20px;
    }
    
    .results-display h2 {
        font-size: 1.8rem;
    }
}
