<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抽奖系统</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎉 抽奖系统 🎉</h1>
        </header>

        <!-- 文件上传区域 -->
        <div class="upload-section" id="uploadSection">
            <div class="upload-area">
                <div class="upload-icon">📁</div>
                <h3>上传Excel文件</h3>
                <p>请上传包含姓名、身份证号码、手机号码的Excel文件</p>
                <input type="file" id="fileInput" accept=".xlsx,.xls" style="display: none;">
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    选择文件
                </button>
            </div>
            <div class="file-info" id="fileInfo" style="display: none;">
                <p>文件名: <span id="fileName"></span></p>
                <p>总人数: <span id="totalCount"></span></p>
                <button class="btn btn-large btn-primary" onclick="startLottery()">🎯 开始抽奖</button>
            </div>
        </div>

        <!-- 抽奖控制区域 -->
        <div class="lottery-section" id="lotterySection" style="display: none;">
            <div class="control-panel">
                <div class="lottery-info">
                    <p>总人数: <span id="totalPeople"></span></p>
                    <p>已抽取: <span id="drawnCount">0</span></p>
                    <p>剩余人数: <span id="remainingCount"></span></p>
                </div>
                <div class="lottery-buttons">
                    <button class="btn btn-large btn-primary" id="firstLotteryBtn" onclick="startFirstLottery()">
                        第一次抽奖 (200人)
                    </button>
                    <button class="btn btn-large btn-secondary" id="secondLotteryBtn" onclick="startSecondLottery()" disabled>
                        第二次抽奖 (30人)
                    </button>
                    <button class="btn btn-medium btn-primary" onclick="exportResults()" id="exportBtn" style="display: none;">
                        📊 导出结果
                    </button>
                    <button class="btn btn-medium btn-danger" onclick="resetLottery()">
                        🔄 重新开始
                    </button>
                </div>
            </div>
        </div>

        <!-- 抽奖结果展示区域 -->
        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="lottery-animation" id="lotteryAnimation" style="display: none;">
                <h2>🎲 正在抽奖中... 🎲</h2>
                <div class="rolling-names" id="rollingNames"></div>
            </div>
            
            <div class="results-display" id="resultsDisplay" style="display: none;">
                <h2 id="resultsTitle">🎊 中奖名单 🎊</h2>
                <div class="winners-grid" id="winnersGrid"></div>
                <button class="btn btn-large btn-primary" onclick="hideResults()">✅ 确认</button>
            </div>
        </div>

        <!-- 历史记录 -->
        <div class="history-section" id="historySection" style="display: none;">
            <h3>抽奖历史</h3>
            <div class="history-tabs">
                <button class="tab-btn active" onclick="showHistory('first')">第一次抽奖 (200人)</button>
                <button class="tab-btn" onclick="showHistory('second')" id="secondHistoryTab" style="display: none;">第二次抽奖 (30人)</button>
            </div>
            <div class="history-content" id="historyContent"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
