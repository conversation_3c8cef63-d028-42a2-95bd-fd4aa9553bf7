<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成示例Excel文件</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 20px;
        }
        button:hover {
            background: #5a6fd8;
        }
        .info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 生成示例Excel文件</h1>
        
        <div class="info">
            <strong>说明：</strong>此工具用于生成测试用的Excel文件，包含虚拟的姓名、身份证号码和手机号码数据。
        </div>
        
        <div class="form-group">
            <label for="recordCount">生成记录数量：</label>
            <select id="recordCount">
                <option value="300">300条记录（测试用）</option>
                <option value="500">500条记录</option>
                <option value="1000" selected>1000条记录（推荐）</option>
                <option value="1500">1500条记录</option>
                <option value="2000">2000条记录</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="fileName">文件名：</label>
            <input type="text" id="fileName" value="抽奖人员名单" placeholder="请输入文件名">
        </div>
        
        <button onclick="generateExcel()">🎯 生成并下载Excel文件</button>
    </div>

    <script>
        // 姓氏列表
        const surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗', '梁', '宋', '郑', '谢', '韩', '唐', '冯', '于', '董', '萧', '程', '曹', '袁', '邓', '许', '傅', '沈', '曾', '彭', '吕', '苏', '卢', '蒋', '蔡', '贾', '丁', '魏', '薛', '叶', '阎'];
        
        // 名字列表
        const givenNames = ['伟', '芳', '娜', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀英', '霞', '平', '刚', '桂英', '建华', '文', '华', '金凤', '素梅', '建国', '丽娟', '春梅', '淑兰', '金兰', '秀兰', '丽华', '玉兰', '桂兰', '秀梅', '丽梅', '秀华', '桂梅', '志强', '志明', '秀珍', '建军', '建设', '建民', '志华', '志勇', '志刚'];

        // 生成随机姓名
        function generateRandomName() {
            const surname = surnames[Math.floor(Math.random() * surnames.length)];
            const givenName = givenNames[Math.floor(Math.random() * givenNames.length)];
            return surname + givenName;
        }

        // 生成随机身份证号码
        function generateRandomIdCard() {
            // 地区代码（使用一些常见的）
            const areaCodes = ['110101', '310101', '440101', '500101', '120101', '210101', '330101', '370101', '420101', '510101'];
            const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];
            
            // 出生年月日（1970-2000年）
            const year = 1970 + Math.floor(Math.random() * 30);
            const month = String(1 + Math.floor(Math.random() * 12)).padStart(2, '0');
            const day = String(1 + Math.floor(Math.random() * 28)).padStart(2, '0');
            
            // 顺序码（随机3位）
            const sequence = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
            
            // 校验码（简化处理，随机生成）
            const checkCodes = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'X'];
            const checkCode = checkCodes[Math.floor(Math.random() * checkCodes.length)];
            
            return areaCode + year + month + day + sequence + checkCode;
        }

        // 生成随机手机号码
        function generateRandomPhone() {
            const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'];
            const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
            const suffix = String(Math.floor(Math.random() * 100000000)).padStart(8, '0');
            return prefix + suffix;
        }

        // 生成Excel文件
        function generateExcel() {
            const count = parseInt(document.getElementById('recordCount').value);
            const fileName = document.getElementById('fileName').value || '抽奖人员名单';
            
            // 生成数据
            const data = [];
            const usedNames = new Set();
            const usedIdCards = new Set();
            const usedPhones = new Set();
            
            for (let i = 0; i < count; i++) {
                let name, idCard, phone;
                
                // 确保姓名不重复
                do {
                    name = generateRandomName();
                } while (usedNames.has(name));
                usedNames.add(name);
                
                // 确保身份证不重复
                do {
                    idCard = generateRandomIdCard();
                } while (usedIdCards.has(idCard));
                usedIdCards.add(idCard);
                
                // 确保手机号不重复
                do {
                    phone = generateRandomPhone();
                } while (usedPhones.has(phone));
                usedPhones.add(phone);
                
                data.push({
                    '您的姓名': name,
                    '请输人您的身份证号码': idCard,
                    '请输人您的手机号码': phone
                });
            }
            
            // 创建工作簿
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(data);
            
            // 设置列宽
            ws['!cols'] = [
                { wch: 15 }, // 姓名列
                { wch: 20 }, // 身份证列
                { wch: 15 }  // 手机号列
            ];
            
            XLSX.utils.book_append_sheet(wb, ws, '人员名单');
            
            // 下载文件
            const finalFileName = `${fileName}.xlsx`;
            XLSX.writeFile(wb, finalFileName);
            
            alert(`✅ 成功生成 ${count} 条记录的Excel文件！\n文件名：${finalFileName}`);
        }
    </script>
</body>
</html>
