<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联合课程项目</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .ga-info {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .ga-basic-tit {
            background: #f8f9fa;
            padding: 20px 30px;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 0; /* 确保没有下边距 */
        }
        
        .ga-basic-tit strong {
            font-size: 24px;
            color: #2c3e50;
            font-weight: 600;
            display: block;
            position: relative;
            z-index: 10; /* 确保标题在上层 */
        }
        
        .content-section {
            padding: 30px;
            display: flex;
            gap: 30px;
            align-items: flex-start;
        }
        
        .text-content {
            flex: 1;
            font-size: 16px;
            line-height: 1.8;
            color: #555;
        }
        
        .text-content p {
            margin-bottom: 20px;
        }
        
        .image-content {
            flex: 0 0 400px;
        }
        
        .image-content img {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .content-section {
                flex-direction: column;
                padding: 20px;
            }
            
            .image-content {
                flex: none;
                width: 100%;
            }
            
            .ga-basic-tit {
                padding: 15px 20px;
            }
            
            .ga-basic-tit strong {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ga-info">
            <div class="ga-basic-tit">
                <strong>项目简介</strong>
            </div>
            <div class="content-section">
                <div class="text-content">
                    <p>乐学GA是全球方向国际学校，校区坐落于吉隆坡的大学城，距离吉隆坡市中心约30分钟，包括马来亚大学（QS排名60，等同于香港科技大学）、UCSI大学（QS排名265，接近中国的郑州大学）以及马来西亚国民大学（UKM）QS排名138，接近在QS排名中排名中国的兰州大学。这一地区的教育资源丰富，学术氛围浓厚，学生能够沉浸在多元文化的环境中，获得深度的学术熏陶。作为乐高坡的教育中心，吉隆坡不仅为学生提供国际化的学术平台，也为他们的未来职业发展提供了广阔的视野和机会。</p>
                    
                    <p>2024年，乐学国际教育集团的全球方向国际学校联合创立《乐学GA自主选择课程》，方便国内中高考学生转轨至海外。GA国际学校提供一流的英式IGCSE课程和英语冲刺课程（AEPP），乐学国际提供中考后备考生片上立于马来西亚的QS排名前200大学的平台，利用新学生在新马的中高企业实习机会，共同打造国内学生在马一条龙的快速通道。我们旨在培养具备全球视野，批判性思维和创新能力的未来领袖。乐学GA深耕不仅关注学术成就，还重视学生全面素质的培养，从数学、科学到文学、历史、音乐艺术、信息技术等多个学科，为学生提供多元化的选择和挑战。每一位学生都能在充满活力与支持的环境中，深化对知识的理解，锻炼解决实际问题的能力。在GA国际学校，学生不仅能获得全球认可的深度环境培养综合素质能力，还能够与世界接轨的学术思维，为未来进入世界一流大学奠定坚实的基础。从第一堂课起，乐学和GA国际学校致力于为学生提供一套独特的教育方案，帮助他们在国际化的学习环境中茁壮成长，成为具备全球视野、创造力和领导力的未来精英。</p>
                </div>
                <div class="image-content">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgdmlld0JveD0iMCAwIDQwMCAzMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0MDAiIGhlaWdodD0iMzAwIiBmaWxsPSIjZjBmOGZmIi8+CjxyZWN0IHg9IjUwIiB5PSI1MCIgd2lkdGg9IjMwMCIgaGVpZ2h0PSIyMDAiIGZpbGw9IiNkZGVlZmYiLz4KPHN2ZyB4PSIxNzUiIHk9IjEyNSIgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjNjY3ZWVhIj4KICA8cGF0aCBkPSJNMjUgNUwzNSAyMEgxNUwyNSA1WiIvPgo8L3N2Zz4KPHRleHQgeD0iMjAwIiB5PSIyNzAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzY2NyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5a2m5qCh5Zu+54mHPC90ZXh0Pgo8L3N2Zz4=" alt="学校图片" />
                </div>
            </div>
        </div>
    </div>
</body>
</html>
