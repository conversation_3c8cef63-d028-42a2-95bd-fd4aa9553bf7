# 🎉 抽奖系统

一个简单易用的网页版抽奖系统，支持从Excel文件导入人员信息，进行两轮抽奖。

## 功能特点

- ✅ **Excel文件导入**：支持.xlsx和.xls格式
- ✅ **两轮抽奖**：第一轮200人，第二轮30人
- ✅ **大屏展示**：适合投影仪和大屏幕显示
- ✅ **抽奖动画**：滚动动画效果，增加仪式感
- ✅ **数据安全**：纯前端运行，数据不上传服务器
- ✅ **结果导出**：支持导出Excel格式的中奖名单
- ✅ **信息脱敏**：显示时自动脱敏身份证和手机号
- ✅ **历史记录**：查看历史抽奖结果

## 使用方法

### 1. 准备Excel文件

Excel文件需要包含以下三个字段（列名可以灵活匹配）：

| 支持的列名 | 说明 |
|-----------|------|
| 姓名 / 您的姓名 / name / 名字 | 参与者姓名 |
| 身份证 / 身份证号码 / 请输人您的身份证号码 / idcard / id | 身份证号码 |
| 手机号 / 手机号码 / 请输人您的手机号码 / phone / 电话 | 手机号码 |

**示例Excel格式：**
```
您的姓名    请输人您的身份证号码    请输人您的手机号码
张三       110101199001011234    13800138000
李四       110101199002022345    13900139000
王五       110101199003033456    13700137000
```

### 2. 打开抽奖系统

1. 双击 `index.html` 文件，在浏览器中打开
2. 或者将整个文件夹部署到Web服务器上

### 3. 导入数据

1. 点击"选择文件"按钮
2. 选择准备好的Excel文件
3. 系统会自动解析并显示总人数
4. 点击"开始抽奖"进入抽奖界面

### 4. 进行抽奖

**第一次抽奖（200人）：**
1. 点击"第一次抽奖 (200人)"按钮
2. 观看抽奖动画
3. 查看中奖名单
4. 点击"确认"关闭结果窗口

**第二次抽奖（30人）：**
1. 点击"第二次抽奖 (30人)"按钮
2. 系统会自动排除第一次的中奖者
3. 观看抽奖动画
4. 查看中奖名单

### 5. 查看和导出结果

- **查看历史**：在页面下方可以切换查看两次抽奖的历史记录
- **导出结果**：点击"导出结果"按钮，下载Excel格式的中奖名单

## 文件结构

```
lottery/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 功能脚本
└── README.md       # 说明文档
```

## 技术特点

- **纯前端实现**：无需服务器，直接在浏览器中运行
- **响应式设计**：支持不同屏幕尺寸
- **Fisher-Yates算法**：确保抽奖的真正随机性
- **数据脱敏**：保护个人隐私信息
- **兼容性好**：支持现代浏览器

## 注意事项

1. **浏览器要求**：建议使用Chrome、Firefox、Safari等现代浏览器
2. **文件格式**：仅支持Excel格式(.xlsx, .xls)
3. **数据完整性**：确保Excel中姓名、身份证、手机号三个字段都有数据
4. **人数限制**：
   - 第一次抽奖需要至少200人
   - 第二次抽奖需要剩余至少30人
5. **数据安全**：所有数据仅在本地浏览器中处理，不会上传到任何服务器

## 常见问题

**Q: 上传Excel后显示"没有找到有效数据"？**
A: 请检查Excel文件的列名是否正确，确保包含姓名、身份证、手机号三个字段。

**Q: 可以修改抽奖人数吗？**
A: 可以修改script.js文件中的数字200和30来调整抽奖人数。

**Q: 抽奖结果是否真正随机？**
A: 是的，使用了Fisher-Yates洗牌算法，确保真正的随机性。

**Q: 可以进行第三次抽奖吗？**
A: 当前版本只支持两次抽奖，如需更多轮次可以联系开发者定制。

## 联系方式

如有问题或需要定制功能，请联系开发者。

---

**祝您抽奖活动圆满成功！** 🎊
