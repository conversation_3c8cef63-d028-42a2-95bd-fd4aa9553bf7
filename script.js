// 全局变量
let allPeople = []; // 所有人员数据
let remainingPeople = []; // 剩余人员
let lotteryResults = []; // 所有抽奖结果 [{round: 1, count: 200, winners: [...]}]
let lotterySettings = {
    totalRounds: 2,
    roundSettings: [
        {round: 1, count: 200, name: '第1轮抽奖'},
        {round: 2, count: 30, name: '第2轮抽奖'}
    ]
};
let currentRound = 1;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    fileInput.addEventListener('change', handleFileUpload);
});

// 处理文件上传
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(firstSheet);
            
            processExcelData(jsonData, file.name);
        } catch (error) {
            alert('文件读取失败，请确保文件格式正确！');
            console.error('文件读取错误:', error);
        }
    };
    reader.readAsArrayBuffer(file);
}

// 处理Excel数据
function processExcelData(data, fileName) {
    if (!data || data.length === 0) {
        alert('Excel文件为空或格式不正确！');
        return;
    }

    // 清理和验证数据
    allPeople = data.map((row, index) => {
        // 获取可能的字段名（支持不同的列名格式）
        const name = getFieldValue(row, ['姓名', '您的姓名', 'name', '名字']);
        const idCard = getFieldValue(row, ['身份证', '身份证号码', '请输人您的身份证号码', 'idcard', 'id']);
        const phone = getFieldValue(row, ['手机号', '手机号码', '请输人您的手机号码', 'phone', '电话']);

        return {
            id: index + 1,
            name: name || '',
            idCard: idCard || '',
            phone: phone || '',
            isValid: !!(name && idCard && phone)
        };
    }).filter(person => person.isValid); // 只保留有效数据

    if (allPeople.length === 0) {
        alert('没有找到有效的数据！请检查Excel文件格式。\n需要包含：姓名、身份证号码、手机号码');
        return;
    }

    // 初始化剩余人员
    remainingPeople = [...allPeople];

    // 显示文件信息
    document.getElementById('fileName').textContent = fileName;
    document.getElementById('totalCount').textContent = allPeople.length;
    document.getElementById('fileInfo').style.display = 'block';

    console.log(`成功加载 ${allPeople.length} 条有效数据`);
}

// 获取字段值（支持多种可能的字段名）
function getFieldValue(row, possibleKeys) {
    for (let key of possibleKeys) {
        if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
            return String(row[key]).trim();
        }
    }
    return null;
}

// 开始抽奖设置
function startLottery() {
    document.getElementById('uploadSection').style.display = 'none';
    document.getElementById('lotterySettings').style.display = 'block';

    // 初始化设置
    updateRoundsSettings();
}

// 更新轮次设置
function updateRoundsSettings() {
    const totalRounds = parseInt(document.getElementById('totalRounds').value);
    const roundsSettings = document.getElementById('roundsSettings');

    let html = '<div class="rounds-config">';

    // 标题行，包含一键设置功能
    html += '<div class="rounds-header">';
    html += '<h4>🎯 各轮抽奖人数设置</h4>';

    // 当轮数大于等于2时显示一键设置功能
    if (totalRounds >= 2) {
        html += `
            <div class="quick-settings-inline">
                <div class="quick-option-inline">
                    <span>每轮</span>
                    <input type="number" id="quickCount" value="10" min="1" max="500" placeholder="人数">
                    <button class="btn btn-mini btn-secondary" onclick="setAllRoundsCount()">确定</button>
                </div>
                <div class="quick-option-inline">
                    <span>递减</span>
                    <input type="number" id="decreaseStart" value="100" min="1" max="500" placeholder="起始">
                    <input type="number" id="decreaseStep" value="10" min="1" max="100" placeholder="递减">
                    <button class="btn btn-mini btn-secondary" onclick="setDecreasingCount()">确定</button>
                </div>
            </div>
        `;
    }

    html += '</div>';

    // 扩展默认人数设置，支持10轮
    const defaultCounts = [200, 30, 20, 15, 10, 8, 6, 5, 3, 2];

    for (let i = 1; i <= totalRounds; i++) {
        const defaultCount = defaultCounts[i-1] || 5;
        html += `
            <div class="round-setting">
                <label>第${i}轮:</label>
                <input type="number" id="round${i}Count" value="${defaultCount}" min="1" max="1000" placeholder="抽奖人数">
                <span>人</span>
            </div>
        `;
    }

    html += '</div>';
    roundsSettings.innerHTML = html;
}

// 一键设置所有轮次相同人数
function setAllRoundsCount() {
    const totalRounds = parseInt(document.getElementById('totalRounds').value);
    const count = parseInt(document.getElementById('quickCount').value);

    if (!count || count < 1) {
        alert('请输入有效的人数！');
        return;
    }

    for (let i = 1; i <= totalRounds; i++) {
        const input = document.getElementById(`round${i}Count`);
        if (input) {
            input.value = count;
        }
    }

    alert(`已将所有轮次设置为 ${count} 人！`);
}

// 递减模式设置
function setDecreasingCount() {
    const totalRounds = parseInt(document.getElementById('totalRounds').value);
    const startCount = parseInt(document.getElementById('decreaseStart').value);
    const decreaseStep = parseInt(document.getElementById('decreaseStep').value);

    if (!startCount || startCount < 1) {
        alert('请输入有效的起始人数！');
        return;
    }

    if (!decreaseStep || decreaseStep < 1) {
        alert('请输入有效的递减数量！');
        return;
    }

    let currentCount = startCount;
    for (let i = 1; i <= totalRounds; i++) {
        const input = document.getElementById(`round${i}Count`);
        if (input) {
            input.value = Math.max(1, currentCount); // 确保不小于1
            currentCount -= decreaseStep;
        }
    }

    alert(`已设置递减模式：从 ${startCount} 人开始，每轮递减 ${decreaseStep} 人！`);
}

// 确认设置
function confirmSettings() {
    const totalRounds = parseInt(document.getElementById('totalRounds').value);

    // 验证设置
    let totalNeeded = 0;
    const roundSettings = [];

    for (let i = 1; i <= totalRounds; i++) {
        const count = parseInt(document.getElementById(`round${i}Count`).value);
        if (!count || count < 1) {
            alert(`请设置第${i}轮的抽奖人数！`);
            return;
        }
        totalNeeded += count;
        roundSettings.push({
            round: i,
            count: count,
            name: `第${i}轮抽奖`
        });
    }

    if (totalNeeded > allPeople.length) {
        alert(`抽奖总人数(${totalNeeded})超过了可用人数(${allPeople.length})！\n请调整各轮的抽奖人数。`);
        return;
    }

    // 保存设置
    lotterySettings = {
        totalRounds: totalRounds,
        roundSettings: roundSettings
    };

    // 重置数据
    lotteryResults = [];
    remainingPeople = [...allPeople];
    currentRound = 1;

    // 显示抽奖控制界面
    document.getElementById('lotterySettings').style.display = 'none';
    document.getElementById('lotterySection').style.display = 'block';
    document.getElementById('historySection').style.display = 'block';

    updateLotteryInfo();
    updateCurrentLotteryInfo();
    updateHistoryTabs();
}

// 更新抽奖信息
function updateLotteryInfo() {
    const totalDrawn = lotteryResults.reduce((sum, result) => sum + result.winners.length, 0);

    document.getElementById('totalPeople').textContent = allPeople.length;
    document.getElementById('drawnCount').textContent = totalDrawn;
    document.getElementById('remainingCount').textContent = remainingPeople.length;

    // 注意：currentRound和maxRounds的更新在updateCurrentLotteryInfo中处理
}

// 更新当前抽奖信息
function updateCurrentLotteryInfo() {
    const roundStatus = document.getElementById('roundStatus');
    const startLotteryBtn = document.getElementById('startLotteryBtn');
    const addRoundBtn = document.getElementById('addRoundBtn');
    const exportBtn = document.getElementById('exportBtn');

    // 检查是否还有未完成的轮次
    const currentSetting = lotterySettings.roundSettings[currentRound - 1];
    if (currentSetting && currentRound <= lotterySettings.totalRounds) {
        document.getElementById('currentLotteryTitle').textContent = currentSetting.name;
        document.getElementById('currentLotteryCount').textContent = currentSetting.count;
        startLotteryBtn.disabled = false;
        startLotteryBtn.textContent = `🎲 开始${currentSetting.name}`;
        startLotteryBtn.style.display = 'inline-block';
        addRoundBtn.style.display = 'none';

        // 显示当前轮次
        roundStatus.innerHTML = `当前轮次: ${currentRound} / ${lotterySettings.totalRounds}`;
    } else {
        document.getElementById('currentLotteryTitle').textContent = '🎊 所有抽奖已完成';
        document.getElementById('currentLotteryCount').textContent = '0';
        startLotteryBtn.style.display = 'none';
        addRoundBtn.style.display = 'inline-block';
        exportBtn.style.display = 'inline-block';

        // 显示已完成状态
        roundStatus.innerHTML = '✅ <strong style="color: #28a745;">已完成</strong>';
    }
}

// 开始当前轮抽奖
function startCurrentLottery() {
    // 检查是否有有效的当前轮次设置
    if (currentRound < 1 || currentRound > lotterySettings.roundSettings.length) {
        alert('当前轮次设置无效！');
        return;
    }

    const currentSetting = lotterySettings.roundSettings[currentRound - 1];
    if (!currentSetting) {
        alert('找不到当前轮次的设置！');
        return;
    }

    const needCount = currentSetting.count;

    if (remainingPeople.length < needCount) {
        alert(`剩余人数不足${needCount}人，当前剩余：${remainingPeople.length}人`);
        return;
    }

    // 禁用按钮
    document.getElementById('startLotteryBtn').disabled = true;

    // 开始抽奖动画
    showLotteryAnimation(`${currentSetting.name} - ${needCount}人`, () => {
        // 执行抽奖
        const winners = drawWinners(remainingPeople, needCount);

        // 保存结果
        lotteryResults.push({
            round: currentRound,
            count: needCount,
            name: currentSetting.name,
            winners: winners
        });

        // 从剩余人员中移除中奖者
        remainingPeople = remainingPeople.filter(person =>
            !winners.some(winner => winner.id === person.id)
        );

        // 显示结果
        showResults(`🎊 ${currentSetting.name}结果 (${needCount}人) 🎊`, winners);

        // 更新轮次
        currentRound++;

        // 更新界面
        updateLotteryInfo();
        updateCurrentLotteryInfo();
        updateHistoryTabs();
        updateHistory();
    });
}

// 抽奖核心算法 - Fisher-Yates洗牌算法
function drawWinners(people, count) {
    const shuffled = [...people];
    
    // Fisher-Yates洗牌
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled.slice(0, count);
}

// 显示抽奖动画
function showLotteryAnimation(title, callback) {
    const resultsSection = document.getElementById('resultsSection');
    const animation = document.getElementById('lotteryAnimation');
    const rollingNames = document.getElementById('rollingNames');
    
    resultsSection.style.display = 'flex';
    animation.style.display = 'block';
    
    // 滚动名字动画
    let animationCount = 0;
    const maxAnimations = 30;
    
    const rollInterval = setInterval(() => {
        if (animationCount >= maxAnimations) {
            clearInterval(rollInterval);
            animation.style.display = 'none';
            callback();
            return;
        }
        
        // 随机显示一个名字
        const randomPerson = remainingPeople[Math.floor(Math.random() * remainingPeople.length)];
        rollingNames.textContent = randomPerson.name;
        animationCount++;
    }, 100);
}

// 显示抽奖结果
function showResults(title, winners) {
    const resultsDisplay = document.getElementById('resultsDisplay');
    const resultsTitle = document.getElementById('resultsTitle');
    const winnersGrid = document.getElementById('winnersGrid');
    
    resultsTitle.textContent = title;
    winnersGrid.innerHTML = '';
    
    // 创建中奖者卡片
    winners.forEach((winner, index) => {
        const card = document.createElement('div');
        card.className = 'winner-card';
        card.innerHTML = `
            <h4>🏆 第${index + 1}名</h4>
            <p><strong>姓名：</strong>${winner.name}</p>
            <p><strong>身份证：</strong>${maskIdCard(winner.idCard)}</p>
            <p><strong>手机：</strong>${maskPhone(winner.phone)}</p>
        `;
        winnersGrid.appendChild(card);
    });
    
    resultsDisplay.style.display = 'block';
}

// 隐藏结果
function hideResults() {
    document.getElementById('resultsSection').style.display = 'none';
}

// 脱敏处理身份证
function maskIdCard(idCard) {
    if (!idCard || idCard.length < 8) return idCard;
    return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
}

// 脱敏处理手机号
function maskPhone(phone) {
    if (!phone || phone.length < 8) return phone;
    return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4);
}

// 显示增加轮次对话框
function showAddRoundDialog() {
    console.log('showAddRoundDialog 函数被调用');
    console.log('剩余人数:', remainingPeople.length);

    const remainingElement = document.getElementById('remainingForNewRound');
    const dialogElement = document.getElementById('addRoundDialog');
    const countElement = document.getElementById('newRoundCount');

    if (!remainingElement || !dialogElement || !countElement) {
        console.error('增加轮次对话框元素未找到');
        console.error('remainingElement:', remainingElement);
        console.error('dialogElement:', dialogElement);
        console.error('countElement:', countElement);
        return;
    }

    remainingElement.textContent = remainingPeople.length;
    dialogElement.style.display = 'flex';

    // 设置默认抽奖人数（不超过剩余人数）
    const defaultCount = Math.min(10, remainingPeople.length);
    countElement.value = defaultCount;
}

// 隐藏增加轮次对话框
function hideAddRoundDialog() {
    document.getElementById('addRoundDialog').style.display = 'none';
}

// 确认增加轮次
function confirmAddRound() {
    console.log('confirmAddRound 函数被调用');

    const countElement = document.getElementById('newRoundCount');
    if (!countElement) {
        console.error('抽奖人数输入框未找到');
        return;
    }

    const newCount = parseInt(countElement.value);
    console.log('输入的抽奖人数:', newCount);

    if (!newCount || newCount < 1) {
        alert('请输入有效的抽奖人数！');
        return;
    }

    if (newCount > remainingPeople.length) {
        alert(`抽奖人数不能超过剩余人数(${remainingPeople.length}人)！`);
        return;
    }

    // 增加新轮次到设置中
    const newRound = lotterySettings.totalRounds + 1;
    lotterySettings.totalRounds = newRound;
    lotterySettings.roundSettings.push({
        round: newRound,
        count: newCount,
        name: `第${newRound}轮抽奖`
    });

    // 重要：设置当前轮次为新增的轮次
    currentRound = newRound;

    // 更新界面
    updateLotteryInfo();
    updateCurrentLotteryInfo();
    updateHistoryTabs();

    // 隐藏对话框
    hideAddRoundDialog();

    alert(`成功增加第${newRound}轮抽奖，将抽取${newCount}人！`);
}

// 更新历史标签
function updateHistoryTabs() {
    const historyTabs = document.getElementById('historyTabs');
    let html = '';

    lotteryResults.forEach((result, index) => {
        const isActive = index === 0 ? 'active' : '';
        html += `
            <button class="tab-btn ${isActive}" onclick="showHistory(${result.round})">
                ${result.name} (${result.count}人)
            </button>
        `;
    });

    historyTabs.innerHTML = html;
}

// 更新历史记录
function updateHistory() {
    if (lotteryResults.length > 0) {
        showHistory(lotteryResults[lotteryResults.length - 1].round);
    }
}

// 显示历史记录
function showHistory(round) {
    const historyContent = document.getElementById('historyContent');
    const tabBtns = document.querySelectorAll('.tab-btn');

    // 更新标签状态
    tabBtns.forEach(btn => btn.classList.remove('active'));

    // 找到对应轮次的结果
    const result = lotteryResults.find(r => r.round === round);
    if (result) {
        // 激活对应标签
        const targetTab = Array.from(tabBtns).find(btn =>
            btn.textContent.includes(`第${round}轮`)
        );
        if (targetTab) {
            targetTab.classList.add('active');
        }

        displayWinnersList(historyContent, result.winners, `${result.name}结果 (${result.count}人)`);
    } else {
        historyContent.innerHTML = '<p style="text-align: center; color: #666;">暂无抽奖记录</p>';
    }
}

// 显示中奖者列表
function displayWinnersList(container, winners, title) {
    if (winners.length === 0) {
        container.innerHTML = `<p style="text-align: center; color: #666;">暂无${title}</p>`;
        return;
    }

    let html = `<h4>${title}</h4><div class="winners-grid">`;

    winners.forEach((winner, index) => {
        html += `
            <div class="winner-card">
                <h4>🏆 第${index + 1}名</h4>
                <p><strong>姓名：</strong>${winner.name}</p>
                <p><strong>身份证：</strong>${maskIdCard(winner.idCard)}</p>
                <p><strong>手机：</strong>${maskPhone(winner.phone)}</p>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// 导出结果
function exportResults() {
    if (lotteryResults.length === 0) {
        alert('暂无抽奖结果可导出！');
        return;
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 为每轮抽奖创建工作表
    lotteryResults.forEach(result => {
        const data = result.winners.map((winner, index) => ({
            '序号': index + 1,
            '姓名': winner.name,
            '身份证号码': winner.idCard,
            '手机号码': winner.phone,
            '抽奖轮次': result.name
        }));

        const ws = XLSX.utils.json_to_sheet(data);
        const sheetName = `${result.name}(${result.count}人)`;
        XLSX.utils.book_append_sheet(wb, ws, sheetName);
    });

    // 汇总结果
    const allWinners = [];
    lotteryResults.forEach(result => {
        result.winners.forEach(winner => {
            allWinners.push({
                ...winner,
                lotteryRound: result.name
            });
        });
    });

    const summaryData = allWinners.map((winner, index) => ({
        '序号': index + 1,
        '姓名': winner.name,
        '身份证号码': winner.idCard,
        '手机号码': winner.phone,
        '抽奖轮次': winner.lotteryRound
    }));

    const ws3 = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, ws3, '全部中奖者');

    // 下载文件
    const fileName = `抽奖结果_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
    XLSX.writeFile(wb, fileName);

    alert('抽奖结果已导出！');
}

// 重新开始
function resetLottery() {
    if (confirm('确定要重新开始吗？这将清除所有抽奖记录！')) {
        // 重置所有数据
        allPeople = [];
        remainingPeople = [];
        lotteryResults = [];
        currentRound = 1;
        lotterySettings = {
            totalRounds: 2,
            roundSettings: [
                {round: 1, count: 200, name: '第1轮抽奖'},
                {round: 2, count: 30, name: '第2轮抽奖'}
            ]
        };

        // 重置界面
        document.getElementById('uploadSection').style.display = 'block';
        document.getElementById('lotterySettings').style.display = 'none';
        document.getElementById('lotterySection').style.display = 'none';
        document.getElementById('historySection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('fileInfo').style.display = 'none';

        // 重置按钮状态
        document.getElementById('startLotteryBtn').disabled = false;
        document.getElementById('exportBtn').style.display = 'none';

        // 清空文件输入
        document.getElementById('fileInput').value = '';

        // 重置设置
        document.getElementById('totalRounds').value = '2';
        updateRoundsSettings();

        alert('已重新开始，请重新上传Excel文件！');
    }
}
