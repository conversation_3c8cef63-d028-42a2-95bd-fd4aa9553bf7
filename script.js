// 全局变量
let allPeople = []; // 所有人员数据
let firstLotteryWinners = []; // 第一次中奖者
let secondLotteryWinners = []; // 第二次中奖者
let remainingPeople = []; // 剩余人员

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('fileInput');
    fileInput.addEventListener('change', handleFileUpload);
});

// 处理文件上传
function handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = new Uint8Array(e.target.result);
            const workbook = XLSX.read(data, { type: 'array' });
            const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
            const jsonData = XLSX.utils.sheet_to_json(firstSheet);
            
            processExcelData(jsonData, file.name);
        } catch (error) {
            alert('文件读取失败，请确保文件格式正确！');
            console.error('文件读取错误:', error);
        }
    };
    reader.readAsArrayBuffer(file);
}

// 处理Excel数据
function processExcelData(data, fileName) {
    if (!data || data.length === 0) {
        alert('Excel文件为空或格式不正确！');
        return;
    }

    // 清理和验证数据
    allPeople = data.map((row, index) => {
        // 获取可能的字段名（支持不同的列名格式）
        const name = getFieldValue(row, ['姓名', '您的姓名', 'name', '名字']);
        const idCard = getFieldValue(row, ['身份证', '身份证号码', '请输人您的身份证号码', 'idcard', 'id']);
        const phone = getFieldValue(row, ['手机号', '手机号码', '请输人您的手机号码', 'phone', '电话']);

        return {
            id: index + 1,
            name: name || '',
            idCard: idCard || '',
            phone: phone || '',
            isValid: !!(name && idCard && phone)
        };
    }).filter(person => person.isValid); // 只保留有效数据

    if (allPeople.length === 0) {
        alert('没有找到有效的数据！请检查Excel文件格式。\n需要包含：姓名、身份证号码、手机号码');
        return;
    }

    // 初始化剩余人员
    remainingPeople = [...allPeople];

    // 显示文件信息
    document.getElementById('fileName').textContent = fileName;
    document.getElementById('totalCount').textContent = allPeople.length;
    document.getElementById('fileInfo').style.display = 'block';

    console.log(`成功加载 ${allPeople.length} 条有效数据`);
}

// 获取字段值（支持多种可能的字段名）
function getFieldValue(row, possibleKeys) {
    for (let key of possibleKeys) {
        if (row[key] !== undefined && row[key] !== null && row[key] !== '') {
            return String(row[key]).trim();
        }
    }
    return null;
}

// 开始抽奖
function startLottery() {
    document.getElementById('uploadSection').style.display = 'none';
    document.getElementById('lotterySection').style.display = 'block';
    document.getElementById('historySection').style.display = 'block';
    
    updateLotteryInfo();
}

// 更新抽奖信息
function updateLotteryInfo() {
    document.getElementById('totalPeople').textContent = allPeople.length;
    document.getElementById('drawnCount').textContent = firstLotteryWinners.length + secondLotteryWinners.length;
    document.getElementById('remainingCount').textContent = remainingPeople.length;
}

// 第一次抽奖
function startFirstLottery() {
    if (remainingPeople.length < 200) {
        alert(`剩余人数不足200人，当前剩余：${remainingPeople.length}人`);
        return;
    }

    // 禁用按钮
    document.getElementById('firstLotteryBtn').disabled = true;
    
    // 开始抽奖动画
    showLotteryAnimation('第一次抽奖 - 200人', () => {
        // 执行抽奖
        firstLotteryWinners = drawWinners(remainingPeople, 200);
        
        // 从剩余人员中移除中奖者
        remainingPeople = remainingPeople.filter(person => 
            !firstLotteryWinners.some(winner => winner.id === person.id)
        );
        
        // 显示结果
        showResults('🎊 第一次抽奖结果 (200人) 🎊', firstLotteryWinners);
        
        // 更新界面
        updateLotteryInfo();
        document.getElementById('secondLotteryBtn').disabled = false;
        document.getElementById('exportBtn').style.display = 'inline-block';
        
        // 更新历史记录
        updateHistory();
    });
}

// 第二次抽奖
function startSecondLottery() {
    if (remainingPeople.length < 30) {
        alert(`剩余人数不足30人，当前剩余：${remainingPeople.length}人`);
        return;
    }

    // 禁用按钮
    document.getElementById('secondLotteryBtn').disabled = true;
    
    // 开始抽奖动画
    showLotteryAnimation('第二次抽奖 - 30人', () => {
        // 执行抽奖
        secondLotteryWinners = drawWinners(remainingPeople, 30);
        
        // 从剩余人员中移除中奖者
        remainingPeople = remainingPeople.filter(person => 
            !secondLotteryWinners.some(winner => winner.id === person.id)
        );
        
        // 显示结果
        showResults('🎊 第二次抽奖结果 (30人) 🎊', secondLotteryWinners);
        
        // 更新界面
        updateLotteryInfo();
        document.getElementById('secondHistoryTab').style.display = 'inline-block';
        
        // 更新历史记录
        updateHistory();
    });
}

// 抽奖核心算法 - Fisher-Yates洗牌算法
function drawWinners(people, count) {
    const shuffled = [...people];
    
    // Fisher-Yates洗牌
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    
    return shuffled.slice(0, count);
}

// 显示抽奖动画
function showLotteryAnimation(title, callback) {
    const resultsSection = document.getElementById('resultsSection');
    const animation = document.getElementById('lotteryAnimation');
    const rollingNames = document.getElementById('rollingNames');
    
    resultsSection.style.display = 'flex';
    animation.style.display = 'block';
    
    // 滚动名字动画
    let animationCount = 0;
    const maxAnimations = 30;
    
    const rollInterval = setInterval(() => {
        if (animationCount >= maxAnimations) {
            clearInterval(rollInterval);
            animation.style.display = 'none';
            callback();
            return;
        }
        
        // 随机显示一个名字
        const randomPerson = remainingPeople[Math.floor(Math.random() * remainingPeople.length)];
        rollingNames.textContent = randomPerson.name;
        animationCount++;
    }, 100);
}

// 显示抽奖结果
function showResults(title, winners) {
    const resultsDisplay = document.getElementById('resultsDisplay');
    const resultsTitle = document.getElementById('resultsTitle');
    const winnersGrid = document.getElementById('winnersGrid');
    
    resultsTitle.textContent = title;
    winnersGrid.innerHTML = '';
    
    // 创建中奖者卡片
    winners.forEach((winner, index) => {
        const card = document.createElement('div');
        card.className = 'winner-card';
        card.innerHTML = `
            <h4>🏆 第${index + 1}名</h4>
            <p><strong>姓名：</strong>${winner.name}</p>
            <p><strong>身份证：</strong>${maskIdCard(winner.idCard)}</p>
            <p><strong>手机：</strong>${maskPhone(winner.phone)}</p>
        `;
        winnersGrid.appendChild(card);
    });
    
    resultsDisplay.style.display = 'block';
}

// 隐藏结果
function hideResults() {
    document.getElementById('resultsSection').style.display = 'none';
}

// 脱敏处理身份证
function maskIdCard(idCard) {
    if (!idCard || idCard.length < 8) return idCard;
    return idCard.substring(0, 4) + '****' + idCard.substring(idCard.length - 4);
}

// 脱敏处理手机号
function maskPhone(phone) {
    if (!phone || phone.length < 8) return phone;
    return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4);
}

// 更新历史记录
function updateHistory() {
    showHistory('first');
}

// 显示历史记录
function showHistory(type) {
    const historyContent = document.getElementById('historyContent');
    const tabBtns = document.querySelectorAll('.tab-btn');

    // 更新标签状态
    tabBtns.forEach(btn => btn.classList.remove('active'));

    if (type === 'first') {
        tabBtns[0].classList.add('active');
        displayWinnersList(historyContent, firstLotteryWinners, '第一次抽奖结果 (200人)');
    } else if (type === 'second') {
        tabBtns[1].classList.add('active');
        displayWinnersList(historyContent, secondLotteryWinners, '第二次抽奖结果 (30人)');
    }
}

// 显示中奖者列表
function displayWinnersList(container, winners, title) {
    if (winners.length === 0) {
        container.innerHTML = `<p style="text-align: center; color: #666;">暂无${title}</p>`;
        return;
    }

    let html = `<h4>${title}</h4><div class="winners-grid">`;

    winners.forEach((winner, index) => {
        html += `
            <div class="winner-card">
                <h4>🏆 第${index + 1}名</h4>
                <p><strong>姓名：</strong>${winner.name}</p>
                <p><strong>身份证：</strong>${maskIdCard(winner.idCard)}</p>
                <p><strong>手机：</strong>${maskPhone(winner.phone)}</p>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

// 导出结果
function exportResults() {
    if (firstLotteryWinners.length === 0 && secondLotteryWinners.length === 0) {
        alert('暂无抽奖结果可导出！');
        return;
    }

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 第一次抽奖结果
    if (firstLotteryWinners.length > 0) {
        const firstData = firstLotteryWinners.map((winner, index) => ({
            '序号': index + 1,
            '姓名': winner.name,
            '身份证号码': winner.idCard,
            '手机号码': winner.phone,
            '抽奖批次': '第一次抽奖'
        }));

        const ws1 = XLSX.utils.json_to_sheet(firstData);
        XLSX.utils.book_append_sheet(wb, ws1, '第一次抽奖(200人)');
    }

    // 第二次抽奖结果
    if (secondLotteryWinners.length > 0) {
        const secondData = secondLotteryWinners.map((winner, index) => ({
            '序号': index + 1,
            '姓名': winner.name,
            '身份证号码': winner.idCard,
            '手机号码': winner.phone,
            '抽奖批次': '第二次抽奖'
        }));

        const ws2 = XLSX.utils.json_to_sheet(secondData);
        XLSX.utils.book_append_sheet(wb, ws2, '第二次抽奖(30人)');
    }

    // 汇总结果
    const allWinners = [...firstLotteryWinners, ...secondLotteryWinners];
    const summaryData = allWinners.map((winner, index) => ({
        '序号': index + 1,
        '姓名': winner.name,
        '身份证号码': winner.idCard,
        '手机号码': winner.phone,
        '抽奖批次': firstLotteryWinners.includes(winner) ? '第一次抽奖' : '第二次抽奖'
    }));

    const ws3 = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(wb, ws3, '全部中奖者');

    // 下载文件
    const fileName = `抽奖结果_${new Date().toLocaleDateString().replace(/\//g, '-')}.xlsx`;
    XLSX.writeFile(wb, fileName);

    alert('抽奖结果已导出！');
}

// 重新开始
function resetLottery() {
    if (confirm('确定要重新开始吗？这将清除所有抽奖记录！')) {
        // 重置所有数据
        allPeople = [];
        firstLotteryWinners = [];
        secondLotteryWinners = [];
        remainingPeople = [];

        // 重置界面
        document.getElementById('uploadSection').style.display = 'block';
        document.getElementById('lotterySection').style.display = 'none';
        document.getElementById('historySection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('fileInfo').style.display = 'none';

        // 重置按钮状态
        document.getElementById('firstLotteryBtn').disabled = false;
        document.getElementById('secondLotteryBtn').disabled = true;
        document.getElementById('exportBtn').style.display = 'none';
        document.getElementById('secondHistoryTab').style.display = 'none';

        // 清空文件输入
        document.getElementById('fileInput').value = '';

        alert('已重新开始，请重新上传Excel文件！');
    }
}
